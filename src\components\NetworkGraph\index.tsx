import { Card, Empty, Spin } from 'antd';
import type { EChartsOption } from 'echarts';
import ReactECharts from 'echarts-for-react';
import React, { useMemo, useState } from 'react';

export interface NetworkGraphProps {
  data: API.NetworkGraphData | null;
  loading?: boolean;
  title?: string;
  height?: number | string;
  onNodeClick?: (nodeData: any) => void;
  onLinkClick?: (linkData: any) => void;
  style?: React.CSSProperties;
  className?: string;
}

// 使用React.memo包装组件，防止不必要的重新渲染
const NetworkGraph: React.FC<NetworkGraphProps> = React.memo(
  ({
    data,
    loading = false,
    title = '关系网络图',
    height = 600,
    onNodeClick,
    onLinkClick,
    style,
    className,
  }) => {
    // 保存节点位置的状态
    const [nodePositions, setNodePositions] = useState<
      Record<string, [number, number]>
    >({});

    // 生成ECharts配置
    const chartOption = useMemo((): EChartsOption => {
      if (!data || !data.nodes.length) {
        return {};
      }

      // 节点颜色映射
      const colorMap: Record<string, string> = {
        mountain: '#8B4513', // 棕色 - 山塬
        water_system: '#4169E1', // 蓝色 - 水系
        historical_element: '#DC143C', // 红色 - 历史要素
        type_dict: '#32CD32', // 绿色 - 类型
        region_dict: '#FFD700', // 金色 - 区域
      };

      // 关系颜色映射
      const relationColorMap: Record<string, string> = {
        选址关联: '#4169E1', // 蓝色
        视线关联: '#9932CC', // 紫色
        历史关联: '#DC143C', // 红色
        功能关联: '#32CD32', // 绿色
        其他关联: '#808080', // 灰色
      };

      // 处理节点数据
      const nodes = data.nodes.map((node) => ({
        id: node.id,
        name: node.name,
        category: node.category,
        symbolSize: Math.max(20, Math.min(60, node.size * 3)), // 节点大小范围 20-60
        itemStyle: {
          color: colorMap[node.type] || node.color || '#666',
        },
        label: {
          show: true,
          fontSize: 12,
          fontWeight: 'bold' as const,
        },
        emphasis: {
          focus: 'adjacency' as const,
          label: {
            fontSize: 14,
          },
        },
        // 如果有保存的位置，则使用保存的位置
        ...(nodePositions[node.id]
          ? {
              x: nodePositions[node.id][0],
              y: nodePositions[node.id][1],
              fixed: true,
            }
          : {}),
      }));

      // 处理连线数据
      const links = data.links.map((link) => ({
        source: link.source,
        target: link.target,
        label: {
          show: true,
          formatter: link.direction || link.term || '',
          fontSize: 10,
          color: '#666',
        },
        lineStyle: {
          color: relationColorMap[link.relation] || link.color || '#999',
          width: Math.max(1, link.weight * 2),
          curveness: 0.1, // 连线弯曲度
        },
        emphasis: {
          lineStyle: {
            width: 4,
          },
          label: {
            fontSize: 12,
            fontWeight: 'bold' as const,
          },
        },
      }));

      // 生成分类数据
      const categories = data.categories.map((category) => ({
        name: category,
      }));

      return {
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: (params: any) => {
            if (params.dataType === 'node') {
              return `
              <div>
                <strong>${params.data.name}</strong><br/>
                类别: ${params.data.category}<br/>
                节点ID: ${params.data.id}
              </div>
            `;
            } else if (params.dataType === 'edge') {
              const linkData = data.links.find(
                (link) =>
                  link.source === params.data.source &&
                  link.target === params.data.target,
              );
              return `
              <div>
                <strong>${linkData?.relation || '关联'}</strong><br/>
                方向: ${linkData?.direction || '无'}<br/>
                词条: ${linkData?.term || '无'}
              </div>
            `;
            }
            return '';
          },
        },
        legend: {
          data: categories.map((cat) => cat.name),
          orient: 'vertical',
          left: 'left',
          top: 'middle',
        },
        series: [
          {
            type: 'graph',
            layout: 'force',
            data: nodes,
            links: links,
            categories: categories,
            roam: true, // 允许缩放和平移
            focusNodeAdjacency: true, // 鼠标悬停时突出显示相邻节点
            draggable: true, // 允许拖拽节点
            force: {
              repulsion: 1000, // 节点间斥力
              gravity: 0.1, // 重力
              edgeLength: 150, // 边长
              layoutAnimation: true, // 启用布局动画，保持节点动态效果
              friction: 0.6, // 适当的摩擦力，允许节点自然移动
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{b}',
            },
            lineStyle: {
              color: 'source',
              curveness: 0.1,
            },
            emphasis: {
              focus: 'adjacency',
              lineStyle: {
                width: 4,
              },
            },
          },
        ],
        animationDuration: 1500,
        animationEasingUpdate: 'quinticInOut',
      };
      // 注意：不要将selectedNode或showDetail等状态添加到依赖数组中，避免点击节点时重新渲染图表
    }, [data, title, nodePositions]);

    // 处理图表事件
    const onEvents = useMemo(() => {
      const events: Record<string, (params: any) => void> = {};

      // 点击事件
      events.click = (params: any) => {
        if (params.dataType === 'node' && onNodeClick) {
          onNodeClick(params.data);
        } else if (params.dataType === 'edge' && onLinkClick) {
          onLinkClick(params.data);
        }
      };

      // 拖拽结束事件，保存节点位置
      events.dragend = (params: any) => {
        if (params.dataType === 'node') {
          setNodePositions((prev) => ({
            ...prev,
            [params.data.id]: [params.data.x, params.data.y],
          }));
        }
      };

      return events;
    }, [onNodeClick, onLinkClick, setNodePositions]);

    // 渲染内容
    const renderContent = () => {
      if (loading) {
        return (
          <div style={{ textAlign: 'center', padding: '100px 0' }}>
            <Spin size="large" />
          </div>
        );
      }

      if (!data || !data.nodes.length) {
        return (
          <Empty description="暂无关系数据" style={{ padding: '100px 0' }} />
        );
      }

      return (
        <ReactECharts
          option={chartOption}
          style={{
            height: typeof height === 'number' ? `${height}px` : height,
          }}
          onEvents={onEvents}
          opts={{ renderer: 'canvas' }}
        />
      );
    };

    return (
      <Card
        title={title}
        style={style}
        className={className}
        bodyStyle={{ padding: 0 }}
      >
        {renderContent()}
      </Card>
    );
  },
  (prevProps, nextProps) => {
    // 只有在数据真正变化时才重新渲染
    // 忽略onNodeClick等函数属性的变化，因为它们通常在父组件重新渲染时会创建新的函数引用
    return (
      prevProps.data === nextProps.data &&
      prevProps.loading === nextProps.loading &&
      prevProps.height === nextProps.height &&
      prevProps.title === nextProps.title
    );
  },
);

export default NetworkGraph;
