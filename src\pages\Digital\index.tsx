import PublicLayout from '@/components/PublicLayout';
import {
  getStatisticsOverview,
  getStatisticsTimeline,
} from '@/services/portal';
import { BarChartOutlined, EnvironmentOutlined } from '@ant-design/icons';
import {
  Card,
  Col,
  DatePicker,
  message,
  Row,
  Select,
  Statistic,
  Typography,
} from 'antd';
import ReactECharts from 'echarts-for-react';
import React, { useEffect, useMemo, useState } from 'react';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const DigitalPage: React.FC = () => {
  const [selectedRegion, setSelectedRegion] = useState<string>('all');
  const [dateRange, setDateRange] = useState<any>(null);
  const [overview, setOverview] = useState<{
    counts?: {
      mountain: number;
      waterSystem: number;
      historicalElement: number;
    };
    regionStats?: Array<{
      region: string;
      regionId: number;
      mountainCount: number;
      waterSystemCount: number;
      historicalElementCount: number;
      total: number;
    }>;
  } | null>(null);
  const [timeline, setTimeline] = useState<API.TimelineData[]>([]);

  const regionId = useMemo(() => {
    if (selectedRegion === 'all') return undefined;
    const n = Number(selectedRegion);
    return Number.isFinite(n) ? n : undefined;
  }, [selectedRegion]);

  useEffect(() => {
    const load = async () => {
      try {
        const [ov, tl] = await Promise.all([
          getStatisticsOverview({ regionId }),
          getStatisticsTimeline({ regionId }),
        ]);
        if (ov.errCode === 0) setOverview(ov.data || null);
        if (tl.errCode === 0) setTimeline(tl.data || []);
      } catch (e: any) {
        message.error(e?.message || '加载统计失败');
      }
    };
    load();
  }, [regionId]);

  // 饼图配置
  const pieOption = {
    title: {
      text: '要素类型分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    legend: {
      orient: 'vertical',
      left: 'left',
    },
    series: [
      {
        name: '要素数量',
        type: 'pie',
        radius: '50%',
        data: [
          { name: '山塬', value: overview?.counts?.mountain || 0 },
          { name: '水系', value: overview?.counts?.waterSystem || 0 },
          { name: '历史要素', value: overview?.counts?.historicalElement || 0 },
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };

  // 柱状图配置
  const barOption = {
    title: {
      text: '区域分布统计',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: (overview?.regionStats || []).map((i) => i.region),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '要素数量',
        type: 'bar',
        data: (overview?.regionStats || []).map((i) => i.total),
        itemStyle: {
          color: '#1890ff',
        },
      },
    ],
  };

  // 时间轴配置
  const timelineOption = {
    title: {
      text: '历史要素时间分布',
      left: 'center',
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      data: timeline.map((t) => String(t.year)),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        name: '建造数量',
        type: 'line',
        data: timeline.map((t) => t.count),
        itemStyle: {
          color: '#52c41a',
        },
        lineStyle: {
          color: '#52c41a',
        },
      },
    ],
  };

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          数字化统计分析
        </Title>

        {/* 筛选区域 */}
        <div className="digital-filters">
          <span style={{ marginRight: 16 }}>区域筛选：</span>
          <Select
            style={{ width: 200, marginRight: 24 }}
            value={selectedRegion}
            onChange={setSelectedRegion}
            placeholder="选择区域"
          >
            <Select.Option value="all">全部区域</Select.Option>
            <Select.Option value="1">西安区域</Select.Option>
            <Select.Option value="2">咸阳区域</Select.Option>
            <Select.Option value="3">宝鸡区域</Select.Option>
            <Select.Option value="4">渭南区域</Select.Option>
          </Select>

          <span style={{ marginRight: 16 }}>时间范围：</span>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            placeholder={['开始时间', '结束时间']}
          />
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="山塬总数"
                value={overview?.counts?.mountain || 0}
                prefix={<EnvironmentOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="水系总数"
                value={overview?.counts?.waterSystem || 0}
                prefix={<EnvironmentOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="历史要素总数"
                value={overview?.counts?.historicalElement || 0}
                prefix={<EnvironmentOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总计"
                value={
                  (overview?.counts?.mountain || 0) +
                  (overview?.counts?.waterSystem || 0) +
                  (overview?.counts?.historicalElement || 0)
                }
                prefix={<BarChartOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表区域 */}
        <div className="digital-charts">
          <div className="chart-container">
            <ReactECharts option={pieOption} style={{ height: '100%' }} />
          </div>
          <div className="chart-container">
            <ReactECharts option={barOption} style={{ height: '100%' }} />
          </div>
        </div>

        <div style={{ marginTop: 24 }}>
          <div className="chart-container">
            <ReactECharts option={timelineOption} style={{ height: '100%' }} />
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default DigitalPage;
